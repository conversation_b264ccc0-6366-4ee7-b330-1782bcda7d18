package Simulator;

import fr.emse.fayol.maqit.simulator.environment.*;
import fr.emse.fayol.maqit.simulator.robot.Robot;
import java.awt.Color;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * ROBOT DE TRANSIT AVEC SYSTÈME D'ENCHÈRES DÉCENTRALISÉ - VERSION SIMPLIFIÉE
 */
public class MyTransitRobot extends MyRobot {

    // ÉTATS SIMPLIFIÉS DU ROBOT
    public enum TransitState {
        FREE, GOING_TO_START, GOING_TO_TRANSIT, GOING_TO_GOAL, RETURNING_TO_CENTER
    }

    // VARIABLES D'ÉTAT ESSENTIELLES
    private TransitState transitState;
    private int transitX, transitY;
    private double batteryLevel = 100.0;
    private boolean isCharging = false;
    private CoordinateurTaches taskCoordinator;
    private long momentDepart;
    private int destinationX, destinationY;

    // CONSTANTES SIMPLIFIÉES
    private static final double MAX_BATTERY = 100.0;
    private static final double CRITICAL_BATTERY_THRESHOLD = 30.0;
    private static final double MOVE_BATTERY_COST = 1.0;
    private static final double PICKUP_BATTERY_COST = 1.0;
    private static final double DEPOSIT_BATTERY_COST = 1.0;
    private static final double CHARGING_RATE = 10.0;
    private static final double TARGET_BATTERY_LEVEL = 80.0;
    private static final int CENTRAL_AREA_X = 10;
    private static final int CENTRAL_AREA_Y = 12;

    // ZONES ET STATIONS SIMPLIFIÉES
    int[][] transitZones = {{12, 10}, {12, 9}, {9, 10}, {9, 9}};
    int[][] chargingStations = {{11, 10}, {13, 9}, {8, 10}, {10, 9}, {2, 2}, {17, 2}, {2, 17}, {17, 17}};

    // DESTINATIONS SIMPLIFIÉES
    private static final Map<String, int[]> DESTINATIONS = new HashMap<String, int[]>() {{
        put("A", new int[]{2, 2}); put("B", new int[]{2, 18}); put("C", new int[]{18, 2}); put("D", new int[]{18, 18});
    }};

    /**
     * MÉTHODE POUR CRÉER UN NOUVEAU ROBOT DE TRANSIT SIMPLIFIÉ
     */
    public MyTransitRobot(String name, int field, int debug, int[] pos, Color color, int rows, int columns, ColorGridEnvironment env, long seed) {
        super(name, field, debug, pos, color, rows, columns, env, seed);
        this.transitState = TransitState.FREE;
        this.batteryLevel = MAX_BATTERY;
        this.taskCoordinator = new CoordinateurTaches(this, env);
        LogManager.getInstance().logAction(name, "Robot de transit initialisé");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE COORDINATEUR DE TÂCHES
     */
    public CoordinateurTaches getTaskCoordinator() { return taskCoordinator; }
    public CoordinateurTaches getCoordinateurTaches() { return taskCoordinator; }

    /**
     * MÉTHODE POUR DIFFUSER UN MESSAGE À TOUS LES ROBOTS
     */
    public void broadcastMessage(Message message) {
        List<Robot> robots = environnement.getRobot();
        int count = 0;
        for (Robot robot : robots) {
            if (robot != this && robot instanceof MyTransitRobot) {
                ((MyTransitRobot)robot).handleMessage(message);
                count++;
            }
        }
        LogManager.getInstance().logCoordination(getName(), "Message diffusé à " + count + " robots");
    }

    /**
     * MÉTHODE POUR ENVOYER UN MESSAGE DIRECT À UN ROBOT
     */
    public void sendDirectMessage(Message message, String targetRobotId) {
        List<Robot> robots = environnement.getRobot();
        for (Robot robot : robots) {
            if (robot instanceof MyTransitRobot && robot.getName().equals(targetRobotId)) {
                ((MyTransitRobot)robot).handleMessage(message);
                LogManager.getInstance().logCoordination(getName(), "Message direct envoyé à " + targetRobotId);
                break;
            }
        }
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE NIVEAU DE BATTERIE
     */
    public double getBatteryLevel() { return batteryLevel; }

    /**
     * MÉTHODE POUR VÉRIFIER SI LE ROBOT A LIVRÉ
     */
    public boolean hasDelivered() { return etatActuel == EtatRobot.LIVRE; }

    /**
     * MÉTHODE POUR TROUVER UNE ZONE DE TRANSIT DISPONIBLE
     */
    private ColorTransitZone findTransitZoneNotFull() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];
            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone tz = (ColorTransitZone) ((ColorCell)cell).getContent();
                if (!tz.isFull()) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return tz;
                }
            }
        }
        return null;
    }

    /**
     * MÉTHODE POUR TROUVER UNE ZONE DE TRANSIT AVEC COLIS
     */
    private ColorTransitZone findTransitZoneWithPackage() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];
            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone tz = (ColorTransitZone) ((ColorCell)cell).getContent();
                if (tz.getPackages().size() > 0) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return tz;
                }
            }
        }
        return null;
    }

    /**
     * MÉTHODE PRINCIPALE D'EXÉCUTION SIMPLIFIÉE
     */
    @Override
    public void step() {
        // Étape 1 : Consommer de la batterie pour le mouvement
        if (batteryLevel > 0) {
            batteryLevel -= MOVE_BATTERY_COST;
            updateRobotColor();
        }

        // Étape 2 : Gestion simplifiée de la batterie
        if (isCharging) {
            if (batteryLevel < TARGET_BATTERY_LEVEL) {
                chargeBattery();
                return;
            } else {
                isCharging = false;
                transitState = TransitState.FREE;
            }
        }

        // Étape 3 : Vérifier si recharge nécessaire
        if (batteryLevel <= CRITICAL_BATTERY_THRESHOLD && !isCharging) {
            goToNearestChargingStation();
            return;
        }

        // Étape 4 : Logique principale selon l'état
        switch (transitState) {
            case FREE:
                handleFreeState();
                break;
            case GOING_TO_START:
                handleGoingToStartState();
                break;
            case GOING_TO_TRANSIT:
                handleGoingToTransitState();
                break;
            case GOING_TO_GOAL:
                handleGoingToGoalState();
                break;
            case RETURNING_TO_CENTER:
                handleReturningToCenterState();
                break;
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT LIBRE
     */
    private void handleFreeState() {
        // Chercher un colis dans une zone de transit
        ColorTransitZone tzWithPackage = findTransitZoneWithPackage();
        if (tzWithPackage != null) {
            transitState = TransitState.GOING_TO_TRANSIT;
            return;
        }

        // Chercher un nouveau colis dans les zones de départ
        ColorStartZone startZone = findStartZoneWithPackage();
        if (startZone != null) {
            transitState = TransitState.GOING_TO_START;
            return;
        }

        // Retourner au centre si rien à faire
        if (getX() != CENTRAL_AREA_X || getY() != CENTRAL_AREA_Y) {
            transitState = TransitState.RETURNING_TO_CENTER;
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS DÉPART
     */
    private void handleGoingToStartState() {
        ColorStartZone startZone = findStartZoneWithPackage();
        if (startZone != null) {
            if (moveTowards(startZone.getX(), startZone.getY())) {
                // Arrivé à la zone de départ, prendre le colis
                ColorPackage pkg = startZone.getPackages().get(0);
                if (pkg != null) {
                    startZone.removePackage(pkg);
                    colisTransporte = pkg;
                    consumeBatteryForPickup();
                    momentDepart = System.currentTimeMillis();

                    int[] goalPos = DESTINATIONS.get(pkg.getDestinationGoalId());
                    if (goalPos != null) {
                        destinationX = goalPos[0];
                        destinationY = goalPos[1];

                        // Décider d'utiliser une zone de transit ou livraison directe
                        ColorTransitZone tz = findTransitZoneNotFull();
                        if (tz != null && shouldUseTransit(destinationX, destinationY)) {
                            transitState = TransitState.GOING_TO_TRANSIT;
                        } else {
                            transitState = TransitState.GOING_TO_GOAL;
                        }
                    }
                }
            }
        } else {
            transitState = TransitState.FREE;
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS TRANSIT
     */
    private void handleGoingToTransitState() {
        if (colisTransporte != null) {
            // Déposer le colis dans la zone de transit
            if (moveTowards(transitX, transitY)) {
                ColorTransitZone tz = findTransitZoneNotFull();
                if (tz != null) {
                    tz.addPackage(colisTransporte);
                    consumeBatteryForDeposit();
                    colisTransporte = null;
                    transitState = TransitState.FREE;
                }
            }
        } else {
            // Récupérer un colis de la zone de transit
            if (moveTowards(transitX, transitY)) {
                ColorTransitZone tz = findTransitZoneWithPackage();
                if (tz != null && tz.getPackages().size() > 0) {
                    colisTransporte = tz.getPackages().get(0);
                    tz.removePackage(colisTransporte);
                    consumeBatteryForPickup();
                    momentDepart = System.currentTimeMillis();

                    int[] goalPos = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                    if (goalPos != null) {
                        destinationX = goalPos[0];
                        destinationY = goalPos[1];
                        transitState = TransitState.GOING_TO_GOAL;
                    }
                }
            }
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS DESTINATION
     */
    private void handleGoingToGoalState() {
        if (colisTransporte != null) {
            if (moveTowards(destinationX, destinationY)) {
                // Livrer le colis
                long deliveryTime = System.currentTimeMillis() - momentDepart;
                double batteryUsed = MAX_BATTERY - batteryLevel;
                
                taskCoordinator.updateRobotEfficiency(this, deliveryTime, batteryUsed);
                
                colisTransporte = null;
                etatActuel = EtatRobot.LIVRE;
                transitState = TransitState.RETURNING_TO_CENTER;
                
                LogManager.getInstance().logAction(getName(), "Colis livré avec succès");
            }
        } else {
            transitState = TransitState.FREE;
        }
    }

    /**
     * MÉTHODE POUR GÉRER LE RETOUR AU CENTRE
     */
    private void handleReturningToCenterState() {
        if (moveTowards(CENTRAL_AREA_X, CENTRAL_AREA_Y)) {
            transitState = TransitState.FREE;
            etatActuel = EtatRobot.LIBRE;
        }
    }

    /**
     * MÉTHODE POUR DÉCIDER D'UTILISER UNE ZONE DE TRANSIT (SIMPLIFIÉE)
     */
    private boolean shouldUseTransit(int destX, int destY) {
        double directDistance = distanceTo(getX(), getY(), destX, destY);
        double transitDistance = distanceTo(getX(), getY(), transitX, transitY) + 
                               distanceTo(transitX, transitY, destX, destY);
        return transitDistance <= directDistance * 1.3; // Marge de 30%
    }

    /**
     * MÉTHODE POUR ALLER À LA STATION DE RECHARGE LA PLUS PROCHE
     */
    private void goToNearestChargingStation() {
        int[] nearest = findNearestChargingStation();
        if (nearest != null) {
            if (moveTowards(nearest[0], nearest[1])) {
                isCharging = true;
            }
        }
    }

    /**
     * MÉTHODE POUR TROUVER LA STATION DE RECHARGE LA PLUS PROCHE
     */
    private int[] findNearestChargingStation() {
        int[] nearest = null;
        double minDistance = Double.MAX_VALUE;
        for (int[] station : chargingStations) {
            double distance = distanceTo(getX(), getY(), station[0], station[1]);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = station;
            }
        }
        return nearest;
    }

    /**
     * MÉTHODE POUR CHARGER LA BATTERIE
     */
    private void chargeBattery() {
        batteryLevel += CHARGING_RATE;
        if (batteryLevel >= MAX_BATTERY) {
            batteryLevel = MAX_BATTERY;
        }
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR CONSOMMER LA BATTERIE LORS DE LA RÉCUPÉRATION
     */
    public void consumeBatteryForPickup() {
        batteryLevel -= PICKUP_BATTERY_COST;
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR CONSOMMER LA BATTERIE LORS DU DÉPÔT
     */
    public void consumeBatteryForDeposit() {
        batteryLevel -= DEPOSIT_BATTERY_COST;
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES REÇUS
     */
    public void handleMessage(Message message) {
        if (message != null) {
            message.process(this);
        }
    }
}
