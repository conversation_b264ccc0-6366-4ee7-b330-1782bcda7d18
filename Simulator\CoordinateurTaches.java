package Simulator;

import java.util.*;
import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

/**
 * COORDINATEUR DE TÂCHES DÉCENTRALISÉ - VERSION SIMPLIFIÉE
 */
public class CoordinateurTaches {

    // RÉFÉRENCES PRINCIPALES
    private MyTransitRobot robotProprietaire;
    private ColorGridEnvironment environnement;

    // STRUCTURES DE DONNÉES SIMPLIFIÉES
    private Map<String, List<Task>> tachesConnues = new HashMap<>();
    private Map<String, Double> scoresEfficaciteRobots = new HashMap<>();
    private Map<String, Enchere> mesOffres = new HashMap<>();
    private Map<String, List<Enchere>> offresRecues = new HashMap<>();
    private Map<String, String> attributionsTaches = new HashMap<>();
    private Set<String> tachesTerminees = new HashSet<>();

    // CONSTANTES SIMPLIFIÉES
    private static final double SEUIL_UTILITE_MINIMUM = 0.3;
    private static final long DUREE_ENCHERE_MS = 1000; // 1 seconde simplifié

    // DESTINATIONS SIMPLIFIÉES
    private static final Map<Integer, int[]> DESTINATIONS_FINALES = new HashMap<>();
    static {
        DESTINATIONS_FINALES.put(1, new int[]{5, 0});
        DESTINATIONS_FINALES.put(2, new int[]{15, 0});
    }

    /**
     * CLASSE TASK SIMPLIFIÉE
     */
    public static class Task {
        private String id;
        private int startX, startY, goalX, goalY;
        private String assignedRobot;
        private long creationTime;
        private ColorPackage packageRef;

        /**
         * MÉTHODE POUR CRÉER UNE NOUVELLE TÂCHE
         */
        public Task(String id, int startX, int startY, int goalX, int goalY, ColorPackage packageRef) {
            this.id = id;
            this.startX = startX;
            this.startY = startY;
            this.goalX = goalX;
            this.goalY = goalY;
            this.packageRef = packageRef;
            this.creationTime = System.currentTimeMillis();
            this.assignedRobot = null;
        }

        // Getters simplifiés
        public String getId() { return id; }
        public int getStartX() { return startX; }
        public int getStartY() { return startY; }
        public int getGoalX() { return goalX; }
        public int getGoalY() { return goalY; }
        public String getAssignedRobot() { return assignedRobot; }
        public long getCreationTime() { return creationTime; }
        public ColorPackage getPackageRef() { return packageRef; }
        public void setAssignedRobot(String robotId) { this.assignedRobot = robotId; }
    }

    /**
     * MÉTHODE POUR CRÉER UN COORDINATEUR SIMPLIFIÉ
     */
    public CoordinateurTaches(MyTransitRobot robotProprietaire, ColorGridEnvironment env) {
        this.robotProprietaire = robotProprietaire;
        this.environnement = env;

        // Initialiser les zones
        tachesConnues.put("A1", new ArrayList<>());
        tachesConnues.put("A2", new ArrayList<>());
        tachesConnues.put("A3", new ArrayList<>());

        // Initialiser l'efficacité du robot
        scoresEfficaciteRobots.put(robotProprietaire.getName(), 1.0);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Coordinateur initialisé");
    }

    /**
     * MÉTHODE POUR TRAITER UNE NOUVELLE TÂCHE
     */
    public void handleNewTask(Task tache) {
        String zoneId = getZoneIdFromTask(tache);
        if (!tachesConnues.containsKey(zoneId)) {
            tachesConnues.put(zoneId, new ArrayList<>());
        }

        // Vérifier si tâche déjà connue
        boolean dejaConnue = false;
        for (Task existante : tachesConnues.get(zoneId)) {
            if (existante.getId().equals(tache.getId())) {
                dejaConnue = true;
                break;
            }
        }

        if (!dejaConnue) {
            tachesConnues.get(zoneId).add(tache);
            LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Nouvelle tâche: " + tache.getId());

            // Faire une offre si possible
            if (peutFaireOffreSurTache(tache)) {
                Enchere offre = genererOffre(tache);
                if (offre != null) {
                    mesOffres.put(tache.getId(), offre);
                    MessageOffre message = new MessageOffre(robotProprietaire, offre);
                    robotProprietaire.broadcastMessage(message);
                    LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                        "Offre de " + String.format("%.2f", offre.getBidAmount()) + " pour " + tache.getId());
                }
            }
        }
    }

    /**
     * MÉTHODE POUR CRÉER UNE TÂCHE À PARTIR D'UN COLIS
     */
    public Task createTask(ColorPackage colis, ColorStartZone zoneDepart, Map<Integer, int[]> destinations) {
        int[] destination = destinations.get(colis.getDestinationGoalId());
        if (destination == null) return null;

        String taskId = "Task-" + colis.getId();
        Task task = new Task(taskId, zoneDepart.getX(), zoneDepart.getY(),
                           destination[0], destination[1], colis);

        String zoneId = colis.getStartZone();
        if (!tachesConnues.containsKey(zoneId)) {
            tachesConnues.put(zoneId, new ArrayList<>());
        }
        tachesConnues.get(zoneId).add(task);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Tâche créée: " + taskId);

        NewTaskMessage message = new NewTaskMessage(robotProprietaire, task);
        robotProprietaire.broadcastMessage(message);

        return task;
    }

    /**
     * MÉTHODE POUR IDENTIFIER LA ZONE D'UNE TÂCHE
     */
    private String getZoneIdFromTask(Task tache) {
        if (tache.getStartX() == 6 && tache.getStartY() == 19) return "A1";
        if (tache.getStartX() == 9 && tache.getStartY() == 19) return "A2";
        if (tache.getStartX() == 12 && tache.getStartY() == 19) return "A3";
        return "A1"; // Par défaut
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI ON PEUT FAIRE UNE OFFRE
     */
    private boolean peutFaireOffreSurTache(Task tache) {
        // Étape 1 : Vérifier si on porte déjà un colis
        if (robotProprietaire.getCarriedPackage() != null) return false;

        // Étape 2 : Vérifier le niveau de batterie
        if (robotProprietaire.getBatteryLevel() < 30.0) return false;

        // Étape 3 : Vérifier si tâche déjà assignée
        if (tache.getAssignedRobot() != null) return false;

        // Étape 4 : Vérifier si tâche terminée
        if (tachesTerminees.contains(tache.getId())) return false;

        return true;
    }

    /**
     * MÉTHODE POUR GÉNÉRER UNE OFFRE SIMPLIFIÉE
     */
    private Enchere genererOffre(Task tache) {
        double utilite = calculerUtilite(tache);
        if (utilite < SEUIL_UTILITE_MINIMUM) return null;

        return new Enchere(robotProprietaire.getName(), tache.getId(), utilite);
    }

    /**
     * MÉTHODE POUR CALCULER L'UTILITÉ SIMPLIFIÉE
     * Étape 1 : Calculer la distance Manhattan
     * Étape 2 : Prendre en compte le niveau de batterie
     * Étape 3 : Combiner les facteurs
     */
    private double calculerUtilite(Task tache) {
        // Distance Manhattan simplifiée
        int distance = Math.abs(robotProprietaire.getX() - tache.getStartX()) +
                      Math.abs(robotProprietaire.getY() - tache.getStartY());

        // Facteur distance (plus proche = mieux)
        double facteurDistance = 1.0 / (1.0 + distance * 0.1);

        // Facteur batterie (plus de batterie = mieux)
        double facteurBatterie = robotProprietaire.getBatteryLevel() / 100.0;

        // Utilité combinée
        return facteurDistance * facteurBatterie;
    }

    /**
     * MÉTHODE POUR TRAITER UNE OFFRE REÇUE
     */
    public void handleBid(Enchere offre) {
        String taskId = offre.getTaskId();
        if (!offresRecues.containsKey(taskId)) {
            offresRecues.put(taskId, new ArrayList<>());
        }
        offresRecues.get(taskId).add(offre);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Offre reçue de " + offre.getRobotId() + " pour " + taskId);

        // Traiter les offres après un délai
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                traiterOffres(taskId);
            }
        }, DUREE_ENCHERE_MS);
    }

    /**
     * MÉTHODE POUR TRAITER LES OFFRES ET ATTRIBUER LA TÂCHE
     */
    private void traiterOffres(String taskId) {
        List<Enchere> offres = offresRecues.get(taskId);
        if (offres == null || offres.isEmpty()) return;

        // Ajouter notre offre si on en a fait une
        if (mesOffres.containsKey(taskId)) {
            offres.add(mesOffres.get(taskId));
        }

        // Trier les offres (meilleure en premier)
        Collections.sort(offres);

        // Attribuer à la meilleure offre
        Enchere meilleureOffre = offres.get(0);
        attributionsTaches.put(taskId, meilleureOffre.getRobotId());

        // Notifier l'attribution
        TaskAssignedMessage message = new TaskAssignedMessage(robotProprietaire, taskId, meilleureOffre.getRobotId());
        robotProprietaire.broadcastMessage(message);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Tâche " + taskId + " attribuée à " + meilleureOffre.getRobotId());
    }

    /**
     * MÉTHODE POUR TRAITER L'ATTRIBUTION D'UNE TÂCHE
     */
    public void handleTaskAssignment(String taskId, String assignedRobotId) {
        attributionsTaches.put(taskId, assignedRobotId);
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Attribution reçue: " + taskId + " -> " + assignedRobotId);
    }

    /**
     * MÉTHODE POUR METTRE À JOUR L'EFFICACITÉ D'UN ROBOT
     */
    public void updateRobotEfficiency(MyTransitRobot robot, long deliveryTime, double batteryUsed) {
        // Calcul d'efficacité simplifié
        double efficiency = 1000.0 / (deliveryTime / 1000.0 + batteryUsed);
        scoresEfficaciteRobots.put(robot.getName(), efficiency);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Efficacité mise à jour pour " + robot.getName() + ": " + String.format("%.2f", efficiency));
    }

    /**
     * MÉTHODE POUR TRAITER L'ÉTAT D'UNE ZONE DE TRANSIT
     */
    public void handleTransitZoneStatus(int x, int y, boolean isFull) {
        String key = x + "," + y;
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Zone transit (" + x + "," + y + ") " + (isFull ? "pleine" : "disponible"));
    }
}

    /**
     * CLASSE INTERNE TASK - REPRÉSENTATION D'UNE TÂCHE DE LIVRAISON
     *
     * Cette classe encapsule toutes les informations nécessaires pour définir une tâche
     * de livraison dans le système. Chaque tâche représente un colis qui doit être
     * transporté d'un point de départ vers une destination finale.
     *
     * Informations stockées :
     * - Identifiant unique de la tâche
     * - Coordonnées de départ et d'arrivée
     * - Robot assigné (si applicable)
     * - Moment de création
     * - Référence vers le colis original
     *
     * Cette classe est statique car elle ne dépend pas de l'instance du coordinateur.
     */
    public static class Task {
        // ATTRIBUTS DE LA TÂCHE
        // Ces variables stockent toutes les informations nécessaires pour définir une tâche

        private String id;                  // Identifiant unique de la tâche (ex: "Task-1", "Task-2")
        private int startX;                 // Coordonnée X du point de départ (où prendre le colis)
        private int startY;                 // Coordonnée Y du point de départ (où prendre le colis)
        private int goalX;                  // Coordonnée X de la destination finale (où livrer le colis)
        private int goalY;                  // Coordonnée Y de la destination finale (où livrer le colis)
        private String assignedRobot;       // Nom du robot assigné à cette tâche (null si non assignée)
        private long creationTime;          // Moment de création de la tâche (en millisecondes)
        private ColorPackage packageRef;    // Référence vers le colis original associé à cette tâche

        /**
         * CONSTRUCTEUR DE LA TÂCHE
         *
         * Ce constructeur crée une nouvelle tâche avec tous les paramètres nécessaires.
         * Il initialise automatiquement le moment de création et laisse le robot assigné
         * à null (la tâche sera assignée plus tard via le système d'enchères).
         *
         * @param id Identifiant unique de la tâche
         * @param startX Coordonnée X du point de départ
         * @param startY Coordonnée Y du point de départ
         * @param goalX Coordonnée X de la destination finale
         * @param goalY Coordonnée Y de la destination finale
         * @param packageRef Référence vers le colis associé
         */
        public Task(String id, int startX, int startY, int goalX, int goalY, ColorPackage packageRef) {
            // Sauvegarder tous les paramètres fournis
            this.id = id;
            this.startX = startX;
            this.startY = startY;
            this.goalX = goalX;
            this.goalY = goalY;
            this.packageRef = packageRef;

            // Enregistrer le moment de création automatiquement
            this.creationTime = System.currentTimeMillis();

            // La tâche n'est pas encore assignée à un robot
            this.assignedRobot = null;
        }

        // MÉTHODES D'ACCÈS (GETTERS)
        // Ces méthodes permettent de lire les informations de la tâche depuis l'extérieur

        /** @return L'identifiant unique de cette tâche */
        public String getId() { return id; }

        /** @return La coordonnée X du point de départ */
        public int getStartX() { return startX; }

        /** @return La coordonnée Y du point de départ */
        public int getStartY() { return startY; }

        /** @return La coordonnée X de la destination finale */
        public int getGoalX() { return goalX; }

        /** @return La coordonnée Y de la destination finale */
        public int getGoalY() { return goalY; }

        /** @return Le nom du robot assigné, ou null si non assignée */
        public String getAssignedRobot() { return assignedRobot; }

        /** @return Le moment de création de la tâche (en millisecondes) */
        public long getCreationTime() { return creationTime; }

        /** @return La référence vers le colis associé à cette tâche */
        public ColorPackage getPackageRef() { return packageRef; }

        // MÉTHODES DE MODIFICATION (SETTERS)
        // Ces méthodes permettent de modifier certaines informations de la tâche

        /**
         * MÉTHODE POUR ASSIGNER UN ROBOT À CETTE TÂCHE
         *
         * Cette méthode est appelée quand un robot gagne l'enchère pour cette tâche.
         * Une fois assignée, la tâche appartient à ce robot jusqu'à ce qu'elle soit terminée.
         *
         * @param robotId L'identifiant du robot qui prend en charge cette tâche
         */
        public void setAssignedRobot(String robotId) {
            this.assignedRobot = robotId;
        }
    }

    /**
     * CONSTRUCTEUR DU COORDINATEUR DE TÂCHES DÉCENTRALISÉ
     *
     * Ce constructeur crée une nouvelle instance du coordinateur pour un robot spécifique.
     * Contrairement à un système centralisé où il y aurait un seul coordinateur pour tous
     * les robots, ici chaque robot a son propre coordinateur qui prend des décisions
     * autonomes basées sur sa situation locale.
     *
     * Étapes d'initialisation :
     * 1. Sauvegarder les références vers le robot propriétaire et l'environnement
     * 2. Initialiser les structures de données pour chaque zone de départ
     * 3. Créer les données d'efficacité initiales pour ce robot
     * 4. Enregistrer la création dans les logs
     *
     * @param robotProprietaire Le robot qui possède cette instance de coordinateur
     * @param env L'environnement de simulation (référence vers la grille)
     */
    public CoordinateurTaches(MyTransitRobot robotProprietaire, ColorGridEnvironment env) {
        // Étape 1 : Sauvegarder les références essentielles
        this.robotProprietaire = robotProprietaire;
        this.environnement = env;

        // Étape 2 : Initialiser les listes de tâches pour chaque zone de départ
        // Chaque zone (A1, A2, A3) aura sa propre liste de tâches connues
        tachesConnues.put("A1", new ArrayList<>());
        tachesConnues.put("A2", new ArrayList<>());
        tachesConnues.put("A3", new ArrayList<>());

        // Étape 3 : Initialiser les données d'efficacité pour ce robot
        String identifiantRobot = robotProprietaire.getName();
        scoresUtiliteRobots.put(identifiantRobot, 1.0);      // Score d'utilité initial
        compteurLivraisonsRobots.put(identifiantRobot, 0);   // Aucune livraison au début
        scoresEfficaciteRobots.put(identifiantRobot, 1.0);   // Efficacité initiale neutre

        // Étape 4 : Enregistrer la création du coordinateur dans les logs
        LogManager.getInstance().logCoordination(identifiantRobot, "Coordinateur décentralisé initialisé");
    }

    /**
     * MÉTHODE POUR OBTENIR LE ROBOT PROPRIÉTAIRE
     *
     * Cette méthode retourne une référence vers le robot qui possède cette instance
     * du coordinateur. Chaque robot a son propre coordinateur dans le système décentralisé.
     *
     * @return Le robot propriétaire de ce coordinateur
     */
    public MyTransitRobot getOwnerRobot() {
        return robotProprietaire;
    }

    /**
     * MÉTHODE POUR DÉFINIR L'ENVIRONNEMENT DE SIMULATION
     *
     * Cette méthode permet d'associer ou de changer la référence vers l'environnement
     * de simulation (la grille). L'environnement est nécessaire pour accéder aux
     * informations sur les positions des zones et des autres robots.
     *
     * @param env La nouvelle référence vers l'environnement de simulation
     */
    public void setEnvironment(ColorGridEnvironment env) {
        this.environnement = env;
    }

    /**
     * MÉTHODE POUR TRAITER L'ANNONCE D'UNE NOUVELLE TÂCHE
     *
     * Cette méthode est appelée quand le robot reçoit l'annonce d'une nouvelle tâche
     * disponible dans le système. Elle analyse la tâche et décide si le robot doit
     * faire une offre (enchère) pour l'obtenir.
     *
     * Processus de traitement :
     * 1. Identifier la zone de départ de la tâche
     * 2. Vérifier si on connaît déjà cette tâche (éviter les doublons)
     * 3. Ajouter la tâche à notre base de connaissances
     * 4. Évaluer si on peut faire une offre sur cette tâche
     * 5. Générer et diffuser notre offre si c'est approprié
     *
     * @param tache La nouvelle tâche annoncée
     */
    public void handleNewTask(Task tache) {
        // Étape 1 : Identifier la zone de départ de cette tâche
        String identifiantZone = getZoneIdFromTask(tache);
        if (!tachesConnues.containsKey(identifiantZone)) {
            tachesConnues.put(identifiantZone, new ArrayList<>());
        }

        // Étape 2 : Vérifier si on connaît déjà cette tâche
        boolean tacheDejaConnue = false;
        for (Task tacheExistante : tachesConnues.get(identifiantZone)) {
            if (tacheExistante.getId().equals(tache.getId())) {
                tacheDejaConnue = true;
                break;
            }
        }

        // Étape 3 : Traiter seulement les nouvelles tâches
        if (!tacheDejaConnue) {
            // Ajouter la tâche à notre base de connaissances
            tachesConnues.get(identifiantZone).add(tache);
            LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Nouvelle tâche reçue: " + tache.getId());

            // Étape 4 : Évaluer si on peut faire une offre sur cette tâche
            if (peutFaireOffreSurTache(tache)) {
                // Étape 5 : Générer notre offre pour cette tâche
                Enchere notreOffre = genererOffre(tache);
                if (notreOffre != null) {
                    // Sauvegarder notre offre dans nos données locales
                    mesOffres.put(tache.getId(), notreOffre);

                    // Diffuser notre offre à tous les autres robots
                    MessageOffre messageOffre = new MessageOffre(robotProprietaire, notreOffre);
                    robotProprietaire.broadcastMessage(messageOffre);

                    // Définir une limite de temps pour cette enchère
                    delaisLimitesEncheres.put(tache.getId(), System.currentTimeMillis() + DUREE_ENCHERE_MS);

                    // Enregistrer notre participation à l'enchère
                    LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Enchère de " +
                                      String.format("%.2f", notreOffre.getBidAmount()) + " pour la tâche " + tache.getId());
                }
            }
        }
    }

    /**
     * MÉTHODE POUR CRÉER UNE NOUVELLE TÂCHE À PARTIR D'UN COLIS
     *
     * Cette méthode transforme un colis (ColorPackage) en une tâche de livraison
     * que les robots peuvent traiter. Elle calcule tous les paramètres nécessaires
     * et diffuse la nouvelle tâche à tous les robots du système.
     *
     * Processus de création :
     * 1. Récupérer les coordonnées de destination du colis
     * 2. Générer un identifiant unique pour la tâche
     * 3. Calculer la priorité de la tâche
     * 4. Créer l'objet Task avec tous les paramètres
     * 5. Ajouter la tâche à notre base de connaissances
     * 6. Diffuser l'annonce de la nouvelle tâche
     *
     * @param colis Le colis à transformer en tâche
     * @param zoneDepart La zone de départ où se trouve le colis
     * @param destinations Map des destinations possibles avec leurs coordonnées
     * @return La tâche créée, ou null si la destination n'existe pas
     */
    public Task createTask(ColorPackage colis, ColorStartZone zoneDepart, Map<Integer, int[]> destinations) {
        // Étape 1 : Récupérer les coordonnées de la destination finale
        int[] positionDestination = destinations.get(colis.getDestinationGoalId());
        if (positionDestination == null) return null; // Destination inconnue

        // Étape 2 : Générer un identifiant unique pour cette tâche
        String identifiantTache = "Task-" + colis.getId();

        // Étape 3 : Créer l'objet Task avec tous les paramètres nécessaires
        Task nouvelleTache = new Task(
            identifiantTache,
            zoneDepart.getX(),           // Position X de départ
            zoneDepart.getY(),           // Position Y de départ
            positionDestination[0],      // Position X de destination
            positionDestination[1],      // Position Y de destination
            colis                        // Référence vers le colis original
        );

        // Étape 5 : Ajouter la tâche à notre base de connaissances
        String identifiantZone = colis.getStartZone();
        if (!tachesConnues.containsKey(identifiantZone)) {
            tachesConnues.put(identifiantZone, new ArrayList<>());
        }
        tachesConnues.get(identifiantZone).add(nouvelleTache);

        // Enregistrer la création de la tâche dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Création de la tâche " + identifiantTache +
                          " à la zone " + identifiantZone + " avec destination " +
                          colis.getDestinationGoalId());

        // Étape 6 : Diffuser l'annonce de la nouvelle tâche à tous les robots
        NewTaskMessage messageNouvelleTache = new NewTaskMessage(robotProprietaire, nouvelleTache);
        robotProprietaire.broadcastMessage(messageNouvelleTache);

        return nouvelleTache;
    }

    /**
     * MÉTHODE POUR IDENTIFIER LA ZONE D'UNE TÂCHE SELON SES COORDONNÉES
     *
     * Cette méthode détermine à quelle zone de départ appartient une tâche
     * en analysant ses coordonnées de départ. C'est important pour organiser
     * les tâches par zone et optimiser les déplacements des robots.
     *
     * Zones de départ connues :
     * - Zone A1 : position [6, 19]
     * - Zone A2 : position [9, 19]
     * - Zone A3 : position [12, 19]
     *
     * @param tache La tâche dont on veut identifier la zone
     * @return L'identifiant de la zone ("A1", "A2", "A3") ou "A1" par défaut
     */
    private String getZoneIdFromTask(Task tache) {
        // Vérifier les coordonnées de départ pour identifier la zone
        if (tache.getStartX() == 6 && tache.getStartY() == 19) {
            return "A1"; // Zone A1 identifiée
        } else if (tache.getStartX() == 9 && tache.getStartY() == 19) {
            return "A2"; // Zone A2 identifiée
        } else if (tache.getStartX() == 12 && tache.getStartY() == 19) {
            return "A3"; // Zone A3 identifiée
        } else {
            // Si on ne reconnaît pas les coordonnées, utiliser A1 par défaut
            return "A1";
        }
    }

    /**
     * MÉTHODE POUR DÉTERMINER SI LE ROBOT PEUT FAIRE UNE OFFRE SUR UNE TÂCHE
     *
     * Cette méthode analyse la situation actuelle du robot et détermine s'il est
     * capable et approprié de faire une offre (enchère) sur une tâche donnée.
     * Elle vérifie plusieurs conditions importantes :
     *
     * 1. Le robot ne doit pas déjà porter un colis
     * 2. Le robot doit avoir suffisamment de batterie
     * 3. La tâche ne doit pas déjà être assignée à un autre robot
     * 4. La tâche ne doit pas déjà être terminée
     *
     * Cette logique évite que les robots fassent des offres inappropriées et
     * optimise l'allocation des tâches dans le système d'enchères décentralisé.
     *
     * @param tache La tâche à évaluer pour une éventuelle offre
     * @return true si le robot peut faire une offre, false sinon
     */
    private boolean peutFaireOffreSurTache(Task tache) {
        // Condition 1 : Ne pas faire d'offre si on porte déjà un colis
        // Un robot ne peut porter qu'un seul colis à la fois
        if (robotProprietaire.getCarriedPackage() != null) {
            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - porte déjà un colis");
            return false;
        }

        // Condition 2 : Ne pas faire d'offre si la batterie est trop faible
        // Un robot avec une batterie faible doit d'abord aller charger
        if (robotProprietaire.getBatteryLevel() < 15.0) {
            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - batterie trop faible (" +
                robotProprietaire.getBatteryLevel() + "%)");
            return false;
        }

        // Condition 3 : Ne pas faire d'offre si la tâche est déjà assignée
        // Évite la concurrence sur des tâches déjà attribuées
        if (tache.getAssignedRobot() != null) {
            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - déjà assignée à " +
                tache.getAssignedRobot());
            return false;
        }

        // Condition 4 : Ne pas faire d'offre si la tâche est déjà terminée
        // Évite les offres inutiles sur des tâches déjà accomplies
        if (tachesTerminees.contains(tache.getId())) {
            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Pas d'offre sur tâche " + tache.getId() + " - tâche déjà terminée");
            return false;
        }

        // Si toutes les conditions sont remplies, le robot peut faire une offre
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Conditions remplies pour faire une offre sur tâche " + tache.getId());
        return true;
    }

    /**
     * MÉTHODE POUR GÉNÉRER UNE OFFRE (ENCHÈRE) POUR UNE TÂCHE
     *
     * Cette méthode calcule la valeur que le robot attribue à une tâche donnée
     * et crée une offre correspondante. Le processus fonctionne ainsi :
     *
     * 1. Calcul de l'utilité de la tâche pour ce robot spécifique
     * 2. Vérification que l'utilité est positive (sinon pas d'offre)
     * 3. Création d'une enchère avec le montant calculé
     *
     * L'utilité prend en compte plusieurs facteurs :
     * - Distance entre le robot et la tâche
     * - Niveau de batterie du robot
     * - Efficacité historique du robot
     * - Charge de travail actuelle
     *
     * Plus l'utilité est élevée, plus le robot est motivé à prendre cette tâche.
     *
     * @param tache La tâche pour laquelle générer une offre
     * @return Une enchère si l'utilité est positive, null sinon
     */
    private Enchere genererOffre(Task tache) {
        // Étape 1 : Calculer l'utilité de cette tâche pour notre robot
        // L'utilité représente à quel point cette tâche est "intéressante" pour nous
        double utilite = calculateUtility(robotProprietaire, tache);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Utilité calculée pour tâche " + tache.getId() + ": " +
            String.format("%.2f", utilite));

        // Étape 2 : Vérifier que l'utilité est positive
        // Si l'utilité est négative ou nulle, la tâche n'est pas intéressante
        if (utilite <= 0) {
            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Pas d'offre pour tâche " + tache.getId() + " - utilité trop faible");
            return null;
        }

        // Étape 3 : Créer l'enchère avec notre identifiant, la tâche et le montant
        Enchere nouvelleOffre = new Enchere(robotProprietaire.getName(), tache.getId(), utilite);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Offre générée pour tâche " + tache.getId() + " avec montant " +
            String.format("%.2f", utilite));

        return nouvelleOffre;
    }



    /**
     * MÉTHODE POUR TROUVER LA MEILLEURE TÂCHE POUR CE ROBOT
     *
     * Cette méthode analyse toutes les tâches disponibles et sélectionne celle
     * qui convient le mieux à ce robot selon plusieurs critères d'optimisation.
     * C'est le cœur de la prise de décision décentralisée.
     *
     * Processus de sélection :
     * 1. Vérifier que le robot peut prendre une nouvelle tâche
     * 2. Trier les zones par distance pour optimiser les calculs
     * 3. Évaluer les tâches dans les zones les plus proches
     * 4. Calculer l'utilité de chaque tâche pour ce robot
     * 5. Générer une offre pour la meilleure tâche trouvée
     *
     * @return La meilleure tâche trouvée, ou null si aucune tâche appropriée
     */
    public Task findBestTaskForRobot() {
        // Vérification 1 : Le robot ne doit pas déjà porter un colis
        if (robotProprietaire.getCarriedPackage() != null) return null;

        // Vérification 2 : Le robot doit avoir suffisamment de batterie
        if (robotProprietaire.getBatteryLevel() < 15.0) {
            return null; // Le robot doit d'abord aller charger
        }

        // Variables pour suivre la meilleure tâche trouvée
        double meilleureUtilite = -1;
        Task meilleureTache = null;

        // Étape 1 : Trier les zones par distance pour optimiser les calculs
        List<Map.Entry<String, List<Task>>> zonesTriees = new ArrayList<>();
        for (Map.Entry<String, List<Task>> entree : tachesConnues.entrySet()) {
            if (!entree.getValue().isEmpty()) {
                zonesTriees.add(entree);
            }
        }

        // Trier les zones par distance croissante
        zonesTriees.sort((entree1, entree2) -> {
            // Récupérer la première tâche de chaque zone pour obtenir les coordonnées
            List<Task> taches1 = entree1.getValue();
            List<Task> taches2 = entree2.getValue();

            if (taches1.isEmpty() || taches2.isEmpty()) {
                return 0; // Impossible de comparer des listes vides
            }

            // Calculer la distance vers chaque zone
            double distance1 = calculateDistance(robotProprietaire.getX(), robotProprietaire.getY(),
                                          taches1.get(0).getStartX(), taches1.get(0).getStartY());
            double distance2 = calculateDistance(robotProprietaire.getX(), robotProprietaire.getY(),
                                          taches2.get(0).getStartX(), taches2.get(0).getStartY());

            return Double.compare(distance1, distance2);
        });

        // Étape 2 : Examiner seulement les 2 zones les plus proches pour optimiser
        int nombreZonesAConsiderer = Math.min(2, zonesTriees.size());
        for (int i = 0; i < nombreZonesAConsiderer; i++) {
            Map.Entry<String, List<Task>> entreeZone = zonesTriees.get(i);
            String identifiantZone = entreeZone.getKey();
            List<Task> tachesDeLaZone = new ArrayList<>(entreeZone.getValue());

            // Étape 3 : Évaluer chaque tâche de cette zone
            for (Task tache : tachesDeLaZone) {
                // Ignorer les tâches déjà assignées ou terminées
                if (tache.getAssignedRobot() != null || tachesTerminees.contains(tache.getId())) {
                    continue;
                }

                // Ignorer les tâches sur lesquelles on a déjà fait une offre en attente
                if (mesOffres.containsKey(tache.getId()) &&
                    System.currentTimeMillis() < delaisLimitesEncheres.getOrDefault(tache.getId(), 0L)) {
                    continue;
                }

                // Calculer l'utilité de cette tâche pour notre robot
                double utiliteCalculee = calculateUtility(robotProprietaire, tache);
                if (utiliteCalculee > meilleureUtilite) {
                    meilleureUtilite = utiliteCalculee;
                    meilleureTache = tache;
                }
            }
        }

        // Étape 4 : Si on a trouvé une bonne tâche, faire une offre
        if (meilleureTache != null) {
            // Générer une offre pour cette tâche
            Enchere notreOffre = new Enchere(robotProprietaire.getName(), meilleureTache.getId(), meilleureUtilite);

            // Sauvegarder notre offre
            mesOffres.put(meilleureTache.getId(), notreOffre);

            // Diffuser notre offre à tous les autres robots
            MessageOffre messageOffre = new MessageOffre(robotProprietaire, notreOffre);
            robotProprietaire.broadcastMessage(messageOffre);

            // Définir une limite de temps pour cette enchère
            delaisLimitesEncheres.put(meilleureTache.getId(), System.currentTimeMillis() + DUREE_ENCHERE_MS);

            // Enregistrer notre participation à l'enchère
            LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Enchère de " +
                              String.format("%.2f", meilleureUtilite) + " pour la tâche " + meilleureTache.getId());
        }

        return meilleureTache;
    }

    /**
     * MÉTHODE POUR TRAITER UNE OFFRE REÇUE D'UN AUTRE ROBOT
     *
     * Cette méthode est appelée quand notre robot reçoit une offre (enchère) d'un autre
     * robot pour une tâche. Elle gère la collecte des offres et déclenche l'évaluation
     * des résultats de l'enchère quand c'est approprié.
     *
     * Processus de traitement :
     * 1. Vérifier que la tâche existe dans notre base de connaissances
     * 2. Ignorer les offres pour des tâches déjà assignées
     * 3. Ajouter l'offre à notre collection d'offres reçues
     * 4. Vérifier si le délai d'enchère est écoulé
     * 5. Traiter les résultats si l'enchère est terminée
     *
     * @param offre L'offre reçue d'un autre robot
     */
    public void handleBid(Enchere offre) {
        String identifiantTache = offre.getTaskId();

        // Étape 1 : Vérifier que nous connaissons cette tâche
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Étape 2 : Ignorer les offres pour des tâches déjà assignées
        if (tache.getAssignedRobot() != null) {
            return; // La tâche est déjà attribuée à quelqu'un
        }

        // Étape 3 : Créer la liste d'offres pour cette tâche si elle n'existe pas
        if (!offresRecues.containsKey(identifiantTache)) {
            offresRecues.put(identifiantTache, new ArrayList<>());
        }

        // Ajouter cette offre à notre collection d'offres reçues
        offresRecues.get(identifiantTache).add(offre);

        // Étape 4 : Vérifier si nous avons nous-mêmes fait une offre sur cette tâche
        Enchere notreOffre = mesOffres.get(identifiantTache);
        if (notreOffre == null) {
            return; // Nous n'avons pas fait d'offre sur cette tâche
        }

        // Étape 5 : Vérifier si le délai d'enchère est écoulé
        if (System.currentTimeMillis() >= delaisLimitesEncheres.getOrDefault(identifiantTache, 0L)) {
            // Le délai est écoulé, traiter les résultats de l'enchère
            processAuctionResults(identifiantTache);
        }
    }

    /**
     * MÉTHODE POUR CALCULER L'UTILITÉ D'UNE TÂCHE POUR UN ROBOT (VERSION SIMPLIFIÉE)
     *
     * Cette méthode évalue l'intérêt d'une tâche pour un robot en utilisant
     * seulement 3 critères essentiels pour un projet étudiant réaliste.
     *
     * Facteurs d'évaluation simplifiés :
     * 1. Distance vers la tâche (80% du poids) - facteur principal
     * 2. Niveau de batterie du robot (15% du poids) - sécurité de base
     * 3. Équilibrage de charge (5% du poids) - répartition équitable
     *
     * @param robot Le robot pour lequel calculer l'utilité
     * @param tache La tâche à évaluer
     * @return Le score d'utilité (plus élevé = plus intéressant)
     */
    private double calculateUtility(MyTransitRobot robot, Task tache) {
        // Facteur 1 : Distance vers la tâche (80% du poids)
        // Plus le robot est proche, plus l'utilité est élevée
        double distanceVersTache = calculateDistance(robot.getX(), robot.getY(), tache.getStartX(), tache.getStartY());

        // Fonction simple : utilité inversement proportionnelle à la distance
        double utiliteDistance = 1.0 / (1.0 + distanceVersTache);

        // Facteur 2 : Niveau de batterie (15% du poids)
        // Vérification simple : le robot a-t-il assez de batterie ?
        double niveauBatterie = robot.getBatteryLevel();
        double utiliteBatterie = (niveauBatterie > 20.0) ? 1.0 : 0.0;

        // Facteur 3 : Équilibrage de charge (5% du poids)
        // Bonus simple pour les robots qui ont fait moins de livraisons
        int nombreLivraisons = compteurLivraisonsRobots.getOrDefault(robot.getName(), 0);
        double bonusEquilibrage = Math.max(0.0, 1.0 - (nombreLivraisons * 0.1));

        // Calcul final simplifié avec les 3 facteurs
        double utiliteFinale = (0.8 * utiliteDistance) +
                              (0.15 * utiliteBatterie) +
                              (0.05 * bonusEquilibrage);

        LogManager.getInstance().logCoordination(robot.getName(),
            String.format("Utilité calculée: %.3f (distance=%.2f, batterie=%.0f%%, livraisons=%d)",
                         utiliteFinale, distanceVersTache, niveauBatterie, nombreLivraisons));

        return utiliteFinale;
    }

    /**
     * MÉTHODE POUR CALCULER LA DISTANCE ENTRE DEUX POINTS
     *
     * Cette méthode calcule la distance de Manhattan (aussi appelée distance de taxi)
     * entre deux points dans la grille. Cette distance représente le nombre minimum
     * de déplacements horizontaux et verticaux nécessaires pour aller d'un point à l'autre.
     *
     * Formule : |x2 - x1| + |y2 - y1|
     *
     * Pourquoi utiliser la distance de Manhattan ?
     * - Les robots se déplacent case par case (pas en diagonal)
     * - C'est la distance réelle que le robot devra parcourir
     * - Plus précise que la distance euclidienne pour notre grille
     *
     * @param x1 Coordonnée X du premier point
     * @param y1 Coordonnée Y du premier point
     * @param x2 Coordonnée X du deuxième point
     * @param y2 Coordonnée Y du deuxième point
     * @return La distance de Manhattan entre les deux points
     */
    private double calculateDistance(int x1, int y1, int x2, int y2) {
        // Calculer la différence absolue en X et en Y
        int differenceX = Math.abs(x1 - x2);
        int differenceY = Math.abs(y1 - y2);

        // La distance est la somme des deux différences
        return differenceX + differenceY;
    }

    /**
     * MÉTHODE POUR TROUVER UNE TÂCHE PAR SON IDENTIFIANT
     *
     * Cette méthode parcourt toutes nos tâches connues pour trouver celle qui
     * correspond à l'identifiant donné. C'est utile pour retrouver une tâche
     * spécifique quand on reçoit des messages qui la mentionnent.
     *
     * Processus de recherche :
     * 1. Parcourir toutes les zones de tâches connues
     * 2. Pour chaque zone, examiner toutes les tâches
     * 3. Comparer l'identifiant de chaque tâche avec celui recherché
     * 4. Retourner la tâche trouvée ou null si aucune correspondance
     *
     * @param identifiantTache L'identifiant de la tâche à trouver
     * @return La tâche correspondante, ou null si non trouvée
     */
    private Task findTaskById(String identifiantTache) {
        // Parcourir toutes les zones de tâches connues
        for (List<Task> listeTaches : tachesConnues.values()) {
            // Pour chaque zone, examiner toutes les tâches
            for (Task tache : listeTaches) {
                // Vérifier si l'identifiant correspond
                if (tache.getId().equals(identifiantTache)) {
                    return tache; // Tâche trouvée !
                }
            }
        }
        return null; // Aucune tâche trouvée avec cet identifiant
    }

    /**
     * MÉTHODE POUR TRAITER LES RÉSULTATS D'UNE ENCHÈRE
     *
     * Cette méthode est appelée quand le délai d'une enchère est écoulé. Elle collecte
     * toutes les offres reçues, détermine le gagnant, et gère les notifications
     * appropriées. C'est le mécanisme central de résolution des enchères décentralisées.
     *
     * Processus de traitement :
     * 1. Collecter toutes les offres pour cette tâche
     * 2. Trier les offres par montant décroissant
     * 3. Déterminer le robot gagnant (offre la plus élevée)
     * 4. Assigner la tâche au gagnant
     * 5. Notifier tous les participants du résultat
     * 6. Nettoyer les données temporaires
     *
     * @param identifiantTache L'identifiant de la tâche dont l'enchère est terminée
     */
    private void processAuctionResults(String identifiantTache) {
        // Étape 1 : Collecter toutes les offres pour cette tâche
        List<Enchere> toutesLesOffres = new ArrayList<>();

        // Ajouter les offres reçues des autres robots
        if (offresRecues.containsKey(identifiantTache)) {
            toutesLesOffres.addAll(offresRecues.get(identifiantTache));
        }

        // Ajouter notre propre offre si nous en avons fait une
        Enchere notreOffre = mesOffres.get(identifiantTache);
        if (notreOffre != null) {
            toutesLesOffres.add(notreOffre);
        }

        // Si aucune offre n'a été faite, ne rien faire
        if (toutesLesOffres.isEmpty()) {
            return;
        }

        // Étape 2 : Trier les offres par montant décroissant (la plus élevée en premier)
        Collections.sort(toutesLesOffres);

        // Étape 3 : Déterminer le gagnant (robot avec l'offre la plus élevée)
        Enchere offreGagnante = toutesLesOffres.get(0);
        String identifiantRobotGagnant = offreGagnante.getRobotId();

        // Étape 4 : Trouver la tâche et l'assigner au gagnant
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Assigner la tâche au robot gagnant
        tache.setAssignedRobot(identifiantRobotGagnant);

        // Étape 5 : Assignation directe simplifiée (sans messages d'acceptation/rejet)
        // Diffuser l'assignation de la tâche à tous les robots
        TaskAssignedMessage messageAssignation = new TaskAssignedMessage(robotProprietaire, identifiantTache, identifiantRobotGagnant);
        robotProprietaire.broadcastMessage(messageAssignation);

        // Enregistrer le résultat dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Tâche " + identifiantTache + " assignée directement à " + identifiantRobotGagnant +
            " (offre: " + String.format("%.2f", offreGagnante.getBidAmount()) + ")");

        // Étape 6 : Nettoyer toutes les données temporaires de cette enchère
        mesOffres.remove(identifiantTache);
        offresRecues.remove(identifiantTache);
        delaisLimitesEncheres.remove(identifiantTache);
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE D'ACCEPTATION D'OFFRE
     *
     * Cette méthode est appelée quand notre robot reçoit confirmation qu'une de ses
     * offres a été acceptée. Elle met à jour l'état de la tâche et nettoie les
     * données temporaires liées à cette enchère.
     *
     * @param identifiantTache L'identifiant de la tâche pour laquelle l'offre a été acceptée
     * @param identifiantRobotGagnant L'identifiant du robot qui a gagné l'enchère
     */
    public void handleBidAccepted(String identifiantTache, String identifiantRobotGagnant) {
        // Trouver la tâche dans notre base de connaissances
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Assigner la tâche au robot gagnant
        tache.setAssignedRobot(identifiantRobotGagnant);

        // Nettoyer toutes les données temporaires liées à cette enchère
        mesOffres.remove(identifiantTache);
        offresRecues.remove(identifiantTache);
        delaisLimitesEncheres.remove(identifiantTache);

        // Enregistrer l'acceptation dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Enchère acceptée pour la tâche " + identifiantTache);
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE DE REJET D'OFFRE
     *
     * Cette méthode est appelée quand notre robot reçoit notification que son offre
     * a été rejetée (un autre robot a fait une meilleure offre). Elle met à jour
     * l'état de la tâche et nettoie les données temporaires.
     *
     * @param identifiantTache L'identifiant de la tâche pour laquelle l'offre a été rejetée
     * @param identifiantRobotGagnant L'identifiant du robot qui a gagné l'enchère
     */
    public void handleBidRejected(String identifiantTache, String identifiantRobotGagnant) {
        // Trouver la tâche dans notre base de connaissances
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Assigner la tâche au robot gagnant
        tache.setAssignedRobot(identifiantRobotGagnant);

        // Nettoyer toutes les données temporaires liées à cette enchère
        mesOffres.remove(identifiantTache);
        offresRecues.remove(identifiantTache);
        delaisLimitesEncheres.remove(identifiantTache);

        // Enregistrer le rejet dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Enchère rejetée pour la tâche " + identifiantTache + ", gagnée par " + identifiantRobotGagnant);
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE D'ASSIGNATION DE TÂCHE
     *
     * Cette méthode est appelée quand notre robot reçoit notification qu'une tâche
     * a été officiellement assignée à un robot spécifique. Elle met à jour notre
     * base de connaissances et nettoie les données temporaires.
     *
     * @param identifiantTache L'identifiant de la tâche qui a été assignée
     * @param identifiantRobotAssigne L'identifiant du robot à qui la tâche a été assignée
     */
    public void handleTaskAssigned(String identifiantTache, String identifiantRobotAssigne) {
        // Trouver la tâche dans notre base de connaissances
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Assigner la tâche au robot désigné
        tache.setAssignedRobot(identifiantRobotAssigne);

        // Nettoyer toutes les données temporaires liées à cette tâche
        mesOffres.remove(identifiantTache);
        offresRecues.remove(identifiantTache);
        delaisLimitesEncheres.remove(identifiantTache);

        // Enregistrer l'assignation dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Tâche " + identifiantTache + " assignée à " + identifiantRobotAssigne);
    }

    /**
     * MÉTHODE POUR METTRE À JOUR LE SCORE D'EFFICACITÉ D'UN ROBOT
     *
     * Cette méthode est appelée après qu'un robot a terminé une livraison. Elle calcule
     * et met à jour le score d'efficacité du robot basé sur sa performance récente.
     * Ces scores sont utilisés pour les futures décisions d'attribution de tâches.
     *
     * Facteurs d'évaluation :
     * 1. Temps de livraison (plus rapide = meilleur score)
     * 2. Consommation de batterie (moins consommé = meilleur score)
     * 3. Moyenne mobile exponentielle pour lisser les variations
     *
     * @param robot Le robot dont on met à jour l'efficacité
     * @param tempsDeLivraison Temps pris pour la livraison (en millisecondes)
     * @param batterieUtilisee Pourcentage de batterie consommé
     */
    public void updateRobotEfficiency(MyTransitRobot robot, long tempsDeLivraison, double batterieUtilisee) {
        String identifiantRobot = robot.getName();

        // Étape 1 : Mettre à jour le compteur de livraisons
        int nombreLivraisons = compteurLivraisonsRobots.getOrDefault(identifiantRobot, 0) + 1;
        compteurLivraisonsRobots.put(identifiantRobot, nombreLivraisons);

        // Étape 2 : Calculer le score basé sur le temps de livraison
        // Plus le temps est court, meilleur est le score
        double scoreTemps = 1.0 / (1.0 + (tempsDeLivraison / 1000.0)); // Convertir ms en secondes

        // Étape 3 : Calculer le score basé sur la consommation de batterie
        // Moins de batterie utilisée = meilleur score
        double scoreBatterie = 1.0 - (batterieUtilisee / 100.0); // Normaliser entre 0 et 1

        // Étape 4 : Combiner les scores avec pondération
        // Le temps est plus important (60%) que la batterie (40%)
        double nouvelleEfficacite = (0.6 * scoreTemps) + (0.4 * scoreBatterie);

        // Étape 5 : Appliquer une moyenne mobile exponentielle pour lisser les variations
        double ancienneEfficacite = scoresEfficaciteRobots.getOrDefault(identifiantRobot, 1.0);
        double efficaciteMiseAJour = (0.7 * nouvelleEfficacite) + (0.3 * ancienneEfficacite);

        // Sauvegarder le nouveau score d'efficacité
        scoresEfficaciteRobots.put(identifiantRobot, efficaciteMiseAJour);

        // Étape 6 : Mettre à jour les statistiques locales
        nombreTachesTermineesLocalement++;
        tempsTotalLivraisonsLocales += tempsDeLivraison;
        batterieUtiliseeTotaleLocale += batterieUtilisee;

        // Enregistrer la mise à jour dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Mise à jour de l'efficacité pour " + identifiantRobot + ": " +
            String.format("%.2f", efficaciteMiseAJour) + " (livraisons: " + nombreLivraisons + ")");

        // Étape 7 : Diffuser la mise à jour d'efficacité à tous les autres robots
        EfficiencyUpdateMessage messageMiseAJour = new EfficiencyUpdateMessage(
            robotProprietaire, identifiantRobot, efficaciteMiseAJour, nombreLivraisons);
        robotProprietaire.broadcastMessage(messageMiseAJour);
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE DE TÂCHE TERMINÉE
     *
     * Cette méthode est appelée quand notre robot reçoit notification qu'un autre robot
     * a terminé une tâche. Elle met à jour notre base de connaissances et les statistiques
     * d'efficacité du robot qui a terminé la tâche.
     *
     * @param identifiantRobot L'identifiant du robot qui a terminé la tâche
     * @param identifiantTache L'identifiant de la tâche terminée
     * @param tempsDeLivraison Temps pris pour la livraison (en millisecondes)
     * @param batterieUtilisee Pourcentage de batterie consommé
     */
    public void handleTaskCompleted(String identifiantRobot, String identifiantTache, long tempsDeLivraison, double batterieUtilisee) {
        // Étape 1 : Trouver la tâche dans notre base de connaissances
        Task tache = findTaskById(identifiantTache);
        if (tache == null) {
            return; // Nous ne connaissons pas cette tâche
        }

        // Étape 2 : Marquer la tâche comme terminée
        tachesTerminees.add(identifiantTache);

        // Étape 3 : Mettre à jour les statistiques du robot qui a terminé
        int nombreLivraisons = compteurLivraisonsRobots.getOrDefault(identifiantRobot, 0) + 1;
        compteurLivraisonsRobots.put(identifiantRobot, nombreLivraisons);

        // Étape 4 : Calculer le nouveau score d'efficacité
        double scoreTemps = 1.0 / (1.0 + (tempsDeLivraison / 1000.0)); // Convertir ms en secondes
        double scoreBatterie = 1.0 - (batterieUtilisee / 100.0); // Normaliser entre 0 et 1

        // Combiner les scores
        double nouvelleEfficacite = (0.6 * scoreTemps) + (0.4 * scoreBatterie);

        // Appliquer une moyenne mobile exponentielle
        double ancienneEfficacite = scoresEfficaciteRobots.getOrDefault(identifiantRobot, 1.0);
        double efficaciteMiseAJour = (0.7 * nouvelleEfficacite) + (0.3 * ancienneEfficacite);

        // Sauvegarder le nouveau score
        scoresEfficaciteRobots.put(identifiantRobot, efficaciteMiseAJour);

        // Enregistrer la completion dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Robot " + identifiantRobot + " a terminé la tâche " + identifiantTache + " en " + (tempsDeLivraison/1000) +
            " secondes avec " + String.format("%.1f", batterieUtilisee) + "% de batterie utilisée");
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE DE MISE À JOUR D'EFFICACITÉ
     *
     * Cette méthode est appelée quand notre robot reçoit une mise à jour des statistiques
     * d'efficacité d'un autre robot. Elle met à jour notre base de connaissances locale
     * avec les nouvelles informations de performance.
     *
     * @param identifiantRobot L'identifiant du robot dont l'efficacité a été mise à jour
     * @param scoreEfficacite Le nouveau score d'efficacité du robot
     * @param nombreLivraisons Le nombre total de livraisons effectuées par ce robot
     */
    public void handleEfficiencyUpdate(String identifiantRobot, double scoreEfficacite, int nombreLivraisons) {
        // Mettre à jour notre connaissance de l'efficacité de ce robot
        scoresEfficaciteRobots.put(identifiantRobot, scoreEfficacite);
        compteurLivraisonsRobots.put(identifiantRobot, nombreLivraisons);

        // Enregistrer la mise à jour dans les logs
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Mise à jour de l'efficacité pour " + identifiantRobot + ": " +
            String.format("%.2f", scoreEfficacite) + " (livraisons: " + nombreLivraisons + ")");
    }

    /**
     * MÉTHODE POUR TRAITER UN MESSAGE DE STATUT DE ZONE DE TRANSIT
     *
     * Cette méthode est appelée quand notre robot reçoit une mise à jour sur l'état
     * d'une zone de transit (pleine ou disponible). Elle met à jour notre connaissance
     * locale de l'état des zones de transit pour optimiser les futures décisions.
     *
     * @param coordonneeXTransit Coordonnée X de la zone de transit
     * @param coordonneeYTransit Coordonnée Y de la zone de transit
     * @param estPleine true si la zone est pleine, false si elle est disponible
     */
    public void handleTransitZoneStatus(int coordonneeXTransit, int coordonneeYTransit, boolean estPleine) {
        // Créer une clé unique pour identifier cette zone de transit
        String cleZoneTransit = coordonneeXTransit + "," + coordonneeYTransit;

        // Mettre à jour notre connaissance de l'état de cette zone
        etatZonesTransit.put(cleZoneTransit, estPleine);

        // Enregistrer le changement d'état dans les logs
        LogManager.getInstance().logTransit(robotProprietaire.getName(),
            "Zone de transit (" + coordonneeXTransit + "," + coordonneeYTransit + ")",
            "est " + (estPleine ? "pleine" : "disponible"));
    }

    /**
     * MÉTHODE SIMPLIFIÉE POUR DÉCIDER SI UNE ZONE DE TRANSIT DOIT ÊTRE UTILISÉE
     *
     * Version simplifiée qui utilise seulement la comparaison de distance
     * pour décider d'utiliser une zone de transit. Plus réaliste pour un projet étudiant.
     *
     * Règle simple : Utiliser le transit si la distance totale via transit
     * est inférieure à 1.2 fois la distance directe.
     *
     * @param robot Le robot qui doit prendre la décision
     * @param destinationX Coordonnée X de la destination finale
     * @param destinationY Coordonnée Y de la destination finale
     * @param transitX Coordonnée X de la zone de transit
     * @param transitY Coordonnée Y de la zone de transit
     * @return true si la zone de transit doit être utilisée, false pour livraison directe
     */
    public boolean shouldUseTransitZone(MyTransitRobot robot, int destinationX, int destinationY, int transitX, int transitY) {
        // Étape 1 : Calculer la distance directe vers la destination
        double distanceDirecte = calculateDistance(robot.getX(), robot.getY(), destinationX, destinationY);

        // Étape 2 : Calculer la distance totale via la zone de transit
        double distanceVersTransit = calculateDistance(robot.getX(), robot.getY(), transitX, transitY);
        double distanceTransitVersDestination = calculateDistance(transitX, transitY, destinationX, destinationY);
        double distanceTotaleViaTransit = distanceVersTransit + distanceTransitVersDestination;

        // Étape 3 : Décision simple basée sur la comparaison de distance
        // Utiliser le transit si le détour est acceptable (moins de 20% de distance supplémentaire)
        boolean utiliserTransit = distanceTotaleViaTransit <= (distanceDirecte * 1.2);

        // Enregistrer la décision dans les logs
        LogManager.getInstance().logTransit(robotProprietaire.getName(), "Décision",
            String.format("Transit: %s (directe=%.1f, transit=%.1f)",
                         utiliserTransit ? "OUI" : "NON", distanceDirecte, distanceTotaleViaTransit));

        return utiliserTransit;
    }

    /**
     * MÉTHODE POUR COMPTER LE NOMBRE DE ROBOTS ACTIFS
     *
     * Cette méthode compte combien de robots sont encore actifs dans le système
     * (n'ont pas encore terminé leurs livraisons). Cette information est utilisée
     * pour les décisions d'équilibrage de charge.
     *
     * @return Le nombre de robots actifs (minimum 1 pour éviter la division par zéro)
     */
    private int countActiveRobots() {
        int nombreRobotsActifs = 0;

        // Parcourir tous les robots connus
        for (String identifiantRobot : scoresEfficaciteRobots.keySet()) {
            // Un robot est considéré comme actif s'il n'a pas encore de livraisons enregistrées
            if (!compteurLivraisonsRobots.containsKey(identifiantRobot)) {
                nombreRobotsActifs++;
            }
        }

        // S'assurer qu'on ne retourne jamais 0 pour éviter les divisions par zéro
        return Math.max(nombreRobotsActifs, 1);
    }

    /**
     * MÉTHODE POUR OBTENIR LES STATISTIQUES D'ANALYSE
     *
     * Cette méthode compile toutes les statistiques locales collectées par ce coordinateur
     * pour permettre l'analyse des performances du système. Ces données sont utiles
     * pour le débogage et l'optimisation du système.
     *
     * @return Une map contenant toutes les statistiques collectées
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistiques = new HashMap<>();

        // Statistiques de base sur les tâches terminées
        statistiques.put("nombreTachesTerminees", nombreTachesTermineesLocalement);

        // Temps moyen de livraison en millisecondes (éviter la division par zéro)
        long tempsMoyenLivraison = nombreTachesTermineesLocalement > 0 ?
            tempsTotalLivraisonsLocales / nombreTachesTermineesLocalement : 0;
        statistiques.put("tempsMoyenLivraison", tempsMoyenLivraison);

        // Consommation moyenne de batterie en pourcentage (éviter la division par zéro)
        int batterieMoyenneUtilisee = nombreTachesTermineesLocalement > 0 ?
            batterieUtiliseeTotaleLocale / nombreTachesTermineesLocalement : 0;
        statistiques.put("batterieMoyenneUtilisee", batterieMoyenneUtilisee);

        // Copies des données d'efficacité de tous les robots connus
        statistiques.put("scoresEfficaciteRobots", new HashMap<>(scoresEfficaciteRobots));
        statistiques.put("nombreLivraisonsParRobot", new HashMap<>(compteurLivraisonsRobots));

        return statistiques;
    }
}
